# 4.0.0-rc.0

* Breaking: Drops support for Node 0.12, we now require at least Node 4.
* Breaking: Update PostCSS to 6.0.0.

# 2.1.0

* postcss-discard-empty will now report the rules that were removed
  (thanks to @duncanbeevers).

# 2.0.1

* Now compiled with babel 6.

# 2.0.0

* Upgraded to PostCSS 5.

# 1.1.2

* Increased performance by iterating the AST in a single pass
  (thanks to @and<PERSON><PERSON><PERSON>).

# 1.1.1

* Tweaks for compatibility with the plugin guidelines.

# 1.1.0

* Now uses the PostCSS `4.1` plugin API.

# 1.0.0

* Initial release.
