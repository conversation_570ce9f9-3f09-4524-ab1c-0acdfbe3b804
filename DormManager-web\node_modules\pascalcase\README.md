# pascalcase [![NPM version](https://badge.fury.io/js/pascalcase.svg)](http://badge.fury.io/js/pascalcase)

> Convert a string to pascal-case.

## Install

Install with [npm](https://www.npmjs.com/)

```sh
$ npm i pascalcase --save
```

## Usage

```js
var pascalcase = require('pascalcase');

pascalcase('a');
//=> 'A'

pascalcase('foo bar baz');
//=> 'FooBarBaz'

pascalcase('foo_bar-baz');
//=> 'FooBarBaz'

pascalcase('foo.bar.baz');
//=> 'FooBarBaz'

pascalcase('foo/bar/baz');
//=> 'FooBarBaz'

pascalcase('foo[bar)baz');
//=> 'FooBarBaz'

pascalcase('#foo+bar*baz');
//=> 'FooBarBaz'

pascalcase('$foo~bar`baz');
//=> 'FooBarBaz'

pascalcase('_foo_bar-baz-');
//=> 'FooBarBaz'
```

## Related projects

* [justified](https://github.com/jonschlinkert/justified): Wrap words to a specified length and justified the text.
* [pad-left](https://github.com/jonschlinkert/pad-left): Left pad a string with zeros or a specified string. Fastest implementation.
* [pad-right](https://github.com/jonschlinkert/pad-right): Right pad a string with zeros or a specified string. Fastest implementation.
* [repeat-string](https://github.com/jonschlinkert/repeat-string): Repeat the given string n times. Fastest implementation for repeating a string.
* [word-wrap](https://github.com/jonschlinkert/word-wrap): Wrap words to a specified length.

## Running tests

Install dev dependencies:

```sh
$ npm i -d && npm test
```

## Contributing

Pull requests and stars are always welcome. For bugs and feature requests, [please create an issue](https://github.com/jonschlinkert/pascalcase/issues/new)

## Author

**Jon Schlinkert**

+ [github/jonschlinkert](https://github.com/jonschlinkert)
+ [twitter/jonschlinkert](http://twitter.com/jonschlinkert)

## License

Copyright © 2015 Jon Schlinkert
Released under the MIT license.

***

_This file was generated by [verb-cli](https://github.com/assemble/verb-cli) on August 19, 2015._