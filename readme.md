操作系统：Windows 10
后端框架：SpringBoot框架
后端软件：IntelliJ IDEA， JDK 1.8， tomcat 9.0
后端所用语言：Java语言
数据库：MySQL
浏览器：谷歌浏览器
前端框架：Vue
前端开发软件：Visual Studio Code
前端UI组件库：Element-ui

共分为管理员、学生、维修员、宿管阿姨4种角色，做成b/s的系统，打开就是一个登录界面，根据选择的身份登录后显示不同的菜单

管理员功能
1、	宿舍楼管理：查看、添加、修改、删除。
2、	宿舍管理：查看、添加、修改、删除。
3、	学生管理：查看、添加、修改、删除。
4、	宿管阿姨管理：查看、添加、修改、删除。
5、	维修员管理：查看、添加、修改、删除。
6、	公告管理：查看、添加、修改、删除。
7、	宿舍更换管理：查看、删除。
8、	报修管理：报修类型管理：查看、添加、修改、删除。报修管理：查看、删除，查看报修的详情和报修进度。
9、	水电费管理：查看、删除。
10、	离校登记管理：查看、删除。
11、	返校登记管理：查看、删除。
12、	宿舍评分管理：查看、删除。
13、	统计报表：报修类型统计饼图，维修员维修统计柱状图，宿舍评分统计柱状图
14、	系统管理：修改登录密码

学生功能
1、	注册登录
2、	查看公告
3、	我的宿舍：查看自己的宿舍，申请更换宿舍，查看自己的申请更换宿舍记录及审核结果
4、	离校登记：提交立登记申请，查看自己的登记记录及审核结果
5、	返校登记：提交返校登记，查看自己的返校登记记录
6、	在线报修：提交报修，查看管理自己的报修记录及报修进度
7、	水电费：查看自己宿舍的水电费，未缴纳状态的可以模拟支付，缴纳
8、	宿舍评分：查看自己的宿舍评分记录
9、	个人中心：修改个人信息，修改登录密码
维修员功能（只处理自己收到的维修）
1、	登录
2、	查看公告
3、	维修管理：待处理维修（更新进度上传图片），已处理维修
4、	个人中心：修改个人信息，修改登录密码

宿管阿姨功能：（只负责自己负责宿舍楼的信息）
1、	登录
2、	查看公告
3、	更换宿舍管理：查看学生提交的申请，进行审核
4、	离校登记管理：查看、审核。
5、	返校登记管理：查看。
6、	水电费管理：查看、添加、修改。
7、	宿舍评分管理：查看、添加、修改。
8、	个人中心：修改个人信息，修改登录密码

注：学生提交报修时，根据学生选择的报修类型，自动分配同类型并且当前维修单最少的维修员
