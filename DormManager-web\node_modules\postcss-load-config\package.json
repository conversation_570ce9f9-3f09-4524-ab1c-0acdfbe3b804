{"name": "postcss-load-config", "version": "2.1.2", "description": "Autoload Config for PostCSS", "main": "src/index.js", "files": ["src"], "engines": {"node": ">= 4"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/postcss/"}, "dependencies": {"cosmiconfig": "^5.0.0", "import-cwd": "^2.0.0"}, "keywords": ["postcss", "postcssrc", "postcss.config.js"], "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "repository": "postcss/postcss-load-config.git", "license": "MIT"}