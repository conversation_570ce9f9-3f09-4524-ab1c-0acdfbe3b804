{"name": "portfinder", "description": "A simple tool to find an open port on the current machine", "version": "1.0.32", "author": "<PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "**************:http-party/node-portfinder.git"}, "keywords": ["http", "ports", "utilities"], "files": ["lib"], "dependencies": {"async": "^2.6.4", "debug": "^3.2.7", "mkdirp": "^0.5.6"}, "devDependencies": {"vows": "^0.8.3"}, "main": "./lib/portfinder", "types": "./lib/portfinder.d.ts", "scripts": {"test": "vows test/*-test.js --spec"}, "engines": {"node": ">= 0.12.0"}, "license": "MIT"}